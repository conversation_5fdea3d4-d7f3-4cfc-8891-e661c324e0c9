import { getAllPosts } from "@marketing/blog/utils/lib/posts";
import { config } from "@repo/config";
import { getBaseUrl } from "@repo/utils";
import type { MetadataRoute } from "next";

const baseUrl = getBaseUrl();
const locales = config.i18n.enabled
	? Object.keys(config.i18n.locales)
	: [config.i18n.defaultLocale];

const staticMarketingPages = ["", "/blog", "/tools/TextCounter"];

// Function to get the correct contact path for each locale
const getContactPath = (locale: string) => {
	return locale === "de" ? "/kontakt" : "/contact";
};

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const posts = await getAllPosts();

	return [
		// Static pages that are the same for all locales
		...staticMarketingPages.flatMap((page) =>
			locales.map((locale) => ({
				url: new URL(`/${locale}${page}`, baseUrl).href,
				lastModified: new Date(),
			})),
		),
		// Contact pages with locale-specific paths
		...locales.map((locale) => ({
			url: new URL(`/${locale}${getContactPath(locale)}`, baseUrl).href,
			lastModified: new Date(),
		})),
		// Blog posts
		...posts.map((post) => ({
			url: new URL(`/${post.locale}/blog/${post.path}`, baseUrl).href,
			lastModified: new Date(),
		})),
	];
}
