"use client";

import { But<PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	BookOpenIcon,
	CheckIcon,
	CopyIcon,
	LightbulbIcon,
	RefreshCwIcon,
	SparklesIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface StoryIdea {
	fandom: string;
	idea: string;
	timestamp: Date;
}

export function StoryIdeaGeneratorTool() {
	const t = useTranslations("storyIdeaGenerator");
	const [fandom, setFandom] = useState("");
	const [generatedIdea, setGeneratedIdea] = useState<StoryIdea | null>(null);
	const [isGenerating, setIsGenerating] = useState(false);
	const [copied, setCopied] = useState(false);

	// Sample story ideas for different fandoms
	const sampleIdeas = [
		"The ancient prophecy is finally within reach...if they're not stopped by a time traveler first.",
		"A cursed artifact, someone with forbidden knowledge, and someone who can't remember their past walk into a magical library...",
		"They must find the lost heir before someone with nothing left to lose does.",
		"The final battle approaches, but first they must survive a dinner party with their worst enemy.",
		"A mysterious letter arrives just as the kingdom falls into eternal winter.",
		"The last dragon egg hatches in the hands of someone who was never meant to find it.",
		"Two rival houses must unite when an ancient evil awakens beneath their city.",
		"A shapeshifter, a mind reader, and a time loop walk into the same coffee shop every Tuesday...",
		"The chosen one discovers they were chosen for the wrong prophecy.",
		"A love potion goes wrong, affecting everyone in the castle except the intended target.",
	];

	const generateStoryIdea = async () => {
		if (!fandom.trim()) {
			return;
		}

		setIsGenerating(true);
		setCopied(false);

		try {
			// Use the AI prompt template provided by the user
			// TODO: Replace this with actual AI API call using the prompt:
			// `Act as a fanfiction story inspiration generator. Create a unique, specific, and conflict-driven fanfiction story idea for me. The story must revolve around the characters and plot of "${fandom.trim()}". Ensure the resulting story is both interesting and creative.
			//
			// Keep it concise. The story idea should be like a movie trailer—short, powerful, and captivating.
			//
			// Refer to the following examples to mimic their style and structure:
			//
			// Example 1: The longship breeze is almost within Sumner's grasp...if they're not thwarted by a bodyguard first.
			//
			// Example 2: An injured person, someone with a gun, and someone with an inhumanly long lifespan walk into a coffeeshop...
			//
			// Example 3: Kehinde must find a god cat before someone with one week left to live does.`

			// For now, simulate API response with sample ideas that follow the format
			await new Promise((resolve) => setTimeout(resolve, 1500));

			// Generate a story idea that follows the prompt format
			const storyTemplates = [
				`The ancient artifact is finally within {character}'s grasp...if they're not stopped by {obstacle} first.`,
				"A {item1}, someone with {trait1}, and someone with {trait2} walk into a {location}...",
				"{character} must find the {object} before someone with {urgency} does.",
				"The final {event} approaches, but first they must survive {challenge}.",
				"A mysterious {item} arrives just as {crisis} begins.",
				"The last {magical_item} awakens in the hands of someone who was never meant to {action} it.",
				"Two rival {groups} must unite when {threat} emerges from {location}.",
				"{character} discovers they were {revelation} all along.",
				"A {spell/item} goes wrong, affecting everyone except {exception}.",
				"The {prophecy/legend} is real, and {character} has {time_limit} to {action} before {consequence}.",
			];

			const randomTemplate =
				storyTemplates[
					Math.floor(Math.random() * storyTemplates.length)
				];

			// Simple template replacement (in real implementation, AI would generate this)
			const generatedIdea = randomTemplate
				.replace(/{character}/g, "the protagonist")
				.replace(/{obstacle}/g, "an unexpected enemy")
				.replace(/{item1}/g, "a cursed artifact")
				.replace(/{trait1}/g, "forbidden knowledge")
				.replace(/{trait2}/g, "nothing left to lose")
				.replace(/{location}/g, "the same place every day")
				.replace(/{object}/g, "lost heir")
				.replace(/{urgency}/g, "one week left to live")
				.replace(/{event}/g, "battle")
				.replace(
					/{challenge}/g,
					"a dinner party with their worst enemy",
				)
				.replace(/{item}/g, "letter")
				.replace(/{crisis}/g, "the kingdom falls into eternal winter")
				.replace(/{magical_item}/g, "dragon egg")
				.replace(/{action}/g, "find")
				.replace(/{groups}/g, "houses")
				.replace(/{threat}/g, "an ancient evil")
				.replace(/{revelation}/g, "chosen for the wrong prophecy")
				.replace(/{spell\/item}/g, "love potion")
				.replace(/{exception}/g, "the intended target")
				.replace(/{prophecy\/legend}/g, "prophecy")
				.replace(/{time_limit}/g, "three days")
				.replace(/{consequence}/g, "all is lost");

			const newIdea: StoryIdea = {
				fandom: fandom.trim(),
				idea: generatedIdea,
				timestamp: new Date(),
			};

			setGeneratedIdea(newIdea);
		} catch (error) {
			console.error("Error generating story idea:", error);
			// Fallback to sample ideas if generation fails
			const fallbackIdea =
				sampleIdeas[Math.floor(Math.random() * sampleIdeas.length)];
			const newIdea: StoryIdea = {
				fandom: fandom.trim(),
				idea: fallbackIdea,
				timestamp: new Date(),
			};
			setGeneratedIdea(newIdea);
		} finally {
			setIsGenerating(false);
		}
	};

	const copyIdea = async () => {
		if (!generatedIdea) {
			return;
		}

		try {
			const textToCopy = `Fandom: ${generatedIdea.fandom}\n\nStory Idea: ${generatedIdea.idea}`;
			await navigator.clipboard.writeText(textToCopy);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			console.error("Failed to copy:", error);
		}
	};

	const tryExampleFandom = () => {
		const exampleFandoms = [
			"Harry Potter",
			"Marvel Cinematic Universe",
			"The Lord of the Rings",
			"Star Wars",
			"Sherlock Holmes",
			"Supernatural",
			"My Hero Academia",
			"Attack on Titan",
		];
		const randomFandom =
			exampleFandoms[Math.floor(Math.random() * exampleFandoms.length)];
		setFandom(randomFandom);
	};

	return (
		<Card className="w-full max-w-4xl mx-auto">
			<CardHeader className="text-center">
				<div className="flex items-center justify-center gap-2 mb-2">
					<LightbulbIcon className="w-8 h-8 text-primary" />
					<CardTitle className="text-2xl">
						{t("form.title")}
					</CardTitle>
				</div>
				<CardDescription className="text-lg">
					{t("form.description")}
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Fandom Input */}
				<div className="space-y-2">
					<div className="flex items-center justify-between">
						<Label
							htmlFor="fandomInput"
							className="text-base font-medium"
						>
							{t("form.fandomInput")}
						</Label>
						<Button
							variant="outline"
							size="sm"
							onClick={tryExampleFandom}
							className="text-xs"
						>
							{t("form.tryExample")}
						</Button>
					</div>
					<Input
						id="fandomInput"
						placeholder={t("form.fandomPlaceholder")}
						value={fandom}
						onChange={(e) => setFandom(e.target.value)}
						className="text-base"
						onKeyDown={(e) => {
							if (
								e.key === "Enter" &&
								!isGenerating &&
								fandom.trim()
							) {
								generateStoryIdea();
							}
						}}
					/>
					<p className="text-xs text-muted-foreground">
						{t("form.fandomHelp")}
					</p>
				</div>

				{/* Generate Button */}
				<div className="flex justify-center">
					<Button
						onClick={generateStoryIdea}
						disabled={!fandom.trim() || isGenerating}
						size="lg"
						className="flex items-center gap-2 text-lg px-8 py-3"
					>
						{isGenerating ? (
							<RefreshCwIcon className="w-5 h-5 animate-spin" />
						) : (
							<SparklesIcon className="w-5 h-5" />
						)}
						{isGenerating
							? t("form.generating")
							: t("form.generate")}
					</Button>
				</div>

				{/* Generated Story Idea */}
				{generatedIdea && (
					<Card className="bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20">
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<BookOpenIcon className="w-5 h-5 text-primary" />
									<CardTitle className="text-lg">
										{t("result.title")}
									</CardTitle>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={copyIdea}
									className="flex items-center gap-2"
								>
									{copied ? (
										<CheckIcon className="w-4 h-4" />
									) : (
										<CopyIcon className="w-4 h-4" />
									)}
									{copied ? t("form.copied") : t("form.copy")}
								</Button>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							<div>
								<Label className="text-sm font-medium text-muted-foreground">
									{t("result.fandom")}
								</Label>
								<p className="text-base font-medium">
									{generatedIdea.fandom}
								</p>
							</div>
							<div>
								<Label className="text-sm font-medium text-muted-foreground">
									{t("result.storyIdea")}
								</Label>
								<p className="text-lg leading-relaxed mt-2 p-4 bg-background/50 rounded-lg border">
									{generatedIdea.idea}
								</p>
							</div>
							<p className="text-xs text-muted-foreground text-center">
								{t("result.generatedAt")}{" "}
								{generatedIdea.timestamp.toLocaleTimeString()}
							</p>
						</CardContent>
					</Card>
				)}

				{/* Tips Section */}
				<Card className="bg-muted/30">
					<CardHeader>
						<CardTitle className="text-lg flex items-center gap-2">
							<LightbulbIcon className="w-5 h-5" />
							{t("tips.title")}
						</CardTitle>
					</CardHeader>
					<CardContent>
						<ul className="space-y-2 text-sm">
							<li className="flex items-start gap-2">
								<span className="text-primary font-bold">
									•
								</span>
								{t("tips.tip1")}
							</li>
							<li className="flex items-start gap-2">
								<span className="text-primary font-bold">
									•
								</span>
								{t("tips.tip2")}
							</li>
							<li className="flex items-start gap-2">
								<span className="text-primary font-bold">
									•
								</span>
								{t("tips.tip3")}
							</li>
						</ul>
					</CardContent>
				</Card>
			</CardContent>
		</Card>
	);
}
