"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { LightbulbIcon, CopyIcon, CheckIcon, SparklesIcon, BookOpenIcon, RefreshCwIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface StoryIdea {
	fandom: string;
	idea: string;
	timestamp: Date;
}

export function StoryIdeaGeneratorTool() {
	const t = useTranslations("storyIdeaGenerator");
	const [fandom, setFandom] = useState("");
	const [generatedIdea, setGeneratedIdea] = useState<StoryIdea | null>(null);
	const [isGenerating, setIsGenerating] = useState(false);
	const [copied, setCopied] = useState(false);

	// Sample story ideas for different fandoms
	const sampleIdeas = [
		"The ancient prophecy is finally within reach...if they're not stopped by a time traveler first.",
		"A cursed artifact, someone with forbidden knowledge, and someone who can't remember their past walk into a magical library...",
		"They must find the lost heir before someone with nothing left to lose does.",
		"The final battle approaches, but first they must survive a dinner party with their worst enemy.",
		"A mysterious letter arrives just as the kingdom falls into eternal winter.",
		"The last dragon egg hatches in the hands of someone who was never meant to find it.",
		"Two rival houses must unite when an ancient evil awakens beneath their city.",
		"A shapeshifter, a mind reader, and a time loop walk into the same coffee shop every Tuesday...",
		"The chosen one discovers they were chosen for the wrong prophecy.",
		"A love potion goes wrong, affecting everyone in the castle except the intended target."
	];

	const generateStoryIdea = async () => {
		if (!fandom.trim()) return;

		setIsGenerating(true);
		setCopied(false);

		try {
			// Simulate API call delay
			await new Promise(resolve => setTimeout(resolve, 1500));
			
			// For now, we'll use a random sample idea
			// In a real implementation, this would call an AI API
			const randomIdea = sampleIdeas[Math.floor(Math.random() * sampleIdeas.length)];
			
			const newIdea: StoryIdea = {
				fandom: fandom.trim(),
				idea: randomIdea,
				timestamp: new Date()
			};

			setGeneratedIdea(newIdea);
		} catch (error) {
			console.error("Error generating story idea:", error);
		} finally {
			setIsGenerating(false);
		}
	};

	const copyIdea = async () => {
		if (!generatedIdea) return;

		try {
			const textToCopy = `Fandom: ${generatedIdea.fandom}\n\nStory Idea: ${generatedIdea.idea}`;
			await navigator.clipboard.writeText(textToCopy);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (error) {
			console.error("Failed to copy:", error);
		}
	};

	const tryExampleFandom = () => {
		const exampleFandoms = [
			"Harry Potter",
			"Marvel Cinematic Universe", 
			"The Lord of the Rings",
			"Star Wars",
			"Sherlock Holmes",
			"Supernatural",
			"My Hero Academia",
			"Attack on Titan"
		];
		const randomFandom = exampleFandoms[Math.floor(Math.random() * exampleFandoms.length)];
		setFandom(randomFandom);
	};

	return (
		<Card className="w-full max-w-4xl mx-auto">
			<CardHeader className="text-center">
				<div className="flex items-center justify-center gap-2 mb-2">
					<LightbulbIcon className="w-8 h-8 text-primary" />
					<CardTitle className="text-2xl">{t("form.title")}</CardTitle>
				</div>
				<CardDescription className="text-lg">
					{t("form.description")}
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Fandom Input */}
				<div className="space-y-2">
					<div className="flex items-center justify-between">
						<Label htmlFor="fandomInput" className="text-base font-medium">
							{t("form.fandomInput")}
						</Label>
						<Button
							variant="outline"
							size="sm"
							onClick={tryExampleFandom}
							className="text-xs"
						>
							{t("form.tryExample")}
						</Button>
					</div>
					<Input
						id="fandomInput"
						placeholder={t("form.fandomPlaceholder")}
						value={fandom}
						onChange={(e) => setFandom(e.target.value)}
						className="text-base"
						onKeyDown={(e) => {
							if (e.key === "Enter" && !isGenerating && fandom.trim()) {
								generateStoryIdea();
							}
						}}
					/>
					<p className="text-xs text-muted-foreground">
						{t("form.fandomHelp")}
					</p>
				</div>

				{/* Generate Button */}
				<div className="flex justify-center">
					<Button
						onClick={generateStoryIdea}
						disabled={!fandom.trim() || isGenerating}
						size="lg"
						className="flex items-center gap-2 text-lg px-8 py-3"
					>
						{isGenerating ? (
							<RefreshCwIcon className="w-5 h-5 animate-spin" />
						) : (
							<SparklesIcon className="w-5 h-5" />
						)}
						{isGenerating ? t("form.generating") : t("form.generate")}
					</Button>
				</div>

				{/* Generated Story Idea */}
				{generatedIdea && (
					<Card className="bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20">
						<CardHeader>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<BookOpenIcon className="w-5 h-5 text-primary" />
									<CardTitle className="text-lg">{t("result.title")}</CardTitle>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={copyIdea}
									className="flex items-center gap-2"
								>
									{copied ? (
										<CheckIcon className="w-4 h-4" />
									) : (
										<CopyIcon className="w-4 h-4" />
									)}
									{copied ? t("form.copied") : t("form.copy")}
								</Button>
							</div>
						</CardHeader>
						<CardContent className="space-y-4">
							<div>
								<Label className="text-sm font-medium text-muted-foreground">
									{t("result.fandom")}
								</Label>
								<p className="text-base font-medium">{generatedIdea.fandom}</p>
							</div>
							<div>
								<Label className="text-sm font-medium text-muted-foreground">
									{t("result.storyIdea")}
								</Label>
								<p className="text-lg leading-relaxed mt-2 p-4 bg-background/50 rounded-lg border">
									{generatedIdea.idea}
								</p>
							</div>
							<p className="text-xs text-muted-foreground text-center">
								{t("result.generatedAt")} {generatedIdea.timestamp.toLocaleTimeString()}
							</p>
						</CardContent>
					</Card>
				)}

				{/* Tips Section */}
				<Card className="bg-muted/30">
					<CardHeader>
						<CardTitle className="text-lg flex items-center gap-2">
							<LightbulbIcon className="w-5 h-5" />
							{t("tips.title")}
						</CardTitle>
					</CardHeader>
					<CardContent>
						<ul className="space-y-2 text-sm">
							<li className="flex items-start gap-2">
								<span className="text-primary font-bold">•</span>
								{t("tips.tip1")}
							</li>
							<li className="flex items-start gap-2">
								<span className="text-primary font-bold">•</span>
								{t("tips.tip2")}
							</li>
							<li className="flex items-start gap-2">
								<span className="text-primary font-bold">•</span>
								{t("tips.tip3")}
							</li>
						</ul>
					</CardContent>
				</Card>
			</CardContent>
		</Card>
	);
}
