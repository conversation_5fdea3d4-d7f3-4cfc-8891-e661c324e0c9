"use client";

import { LocaleLink } from "@i18n/routing";
import { Logo } from "@shared/components/Logo";
import { useLocale, useTranslations } from "next-intl";

export function Footer() {
	const t = useTranslations("common.footer");
	const tCommon = useTranslations("common");
	const locale = useLocale();
	const appName = tCommon("appName");

	// Get the correct contact path based on locale
	const getContactPath = () => {
		return locale === "de" ? "/kontakt" : "/contact";
	};

	return (
		<footer className="border-t py-8 text-foreground/60 text-sm">
			<div className="container grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
				<div>
					<Logo className="opacity-70 grayscale" />
					<p className="mt-3 text-sm opacity-70">
						© {new Date().getFullYear()} {appName}. .
					</p>
				</div>

				<div className="flex flex-col gap-2">
					<h4 className="font-semibold text-foreground mb-2">
						{t("product")}
					</h4>
					<a
						href="/#fanfic-generator-form"
						className="block hover:text-foreground"
					>
						{t("createFanfic")}
					</a>
					<a
						href="/#anleitung"
						className="block hover:text-foreground"
					>
						{t("howItWorks")}
					</a>
					<a href="/#preise" className="block hover:text-foreground">
						{t("pricing")}
					</a>
					<a href="/#fragen" className="block hover:text-foreground">
						{t("faq")}
					</a>
				</div>

				<div className="flex flex-col gap-2">
					<h4 className="font-semibold text-foreground mb-2">
						{t("freeTools")}
					</h4>
					<LocaleLink
						href="/tools/TextCounter"
						className="block hover:text-foreground"
					>
						{t("speechTimer")}
					</LocaleLink>
				</div>

				<div className="flex flex-col gap-2">
					<h4 className="font-semibold text-foreground mb-2">
						{t("company")}
					</h4>
					<LocaleLink
						href="/blog"
						className="block hover:text-foreground"
					>
						{t("blog")}
					</LocaleLink>
					<LocaleLink
						href={getContactPath()}
						className="block hover:text-foreground"
					>
						{t("contact")}
					</LocaleLink>
				</div>

				{/* <div className="flex flex-col gap-2">
					<h4 className="font-semibold text-foreground mb-2">Rechtliches</h4>
					<LocaleLink href="/legal/privacy-policy" className="block hover:text-foreground">
						Datenschutz
					</LocaleLink>
					<LocaleLink href="/legal/terms" className="block hover:text-foreground">
						AGB
					</LocaleLink>
					<LocaleLink href="/legal/impressum" className="block hover:text-foreground">
						Impressum
					</LocaleLink>
				</div> */}
			</div>
		</footer>
	);
}
