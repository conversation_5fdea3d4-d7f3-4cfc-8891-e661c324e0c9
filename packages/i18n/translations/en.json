{"admin": {"menu": {"organizations": "Organizations", "users": "Users"}, "organizations": {"backToList": "Back to organizations", "confirmDelete": {"confirm": "Delete", "message": "Are you sure you want to delete this organization? This action cannot be undone.", "title": "Delete organization"}, "create": "Create", "delete": "Delete", "deleteOrganization": {"deleted": "Organization has been deleted successfully!", "deleting": "Deleting organization...", "notDeleted": "Organization could not be deleted. Please try again."}, "edit": "Edit", "form": {"createTitle": "Create an organization", "name": "Organization name", "notifications": {"error": "Could not save the organization. Please try again later.", "success": "Organization has been saved."}, "save": "Save", "updateTitle": "Edit organization"}, "loading": "Loading organizations...", "membersCount": "{count} {count, plural, one {member} other {members}}", "search": "Search for an organization...", "title": "Manage organizations"}, "title": "Administration", "users": {"confirmDelete": {"confirm": "Delete", "message": "Are you sure you want to delete this user? This action cannot be undone.", "title": "Delete user"}, "delete": "Delete", "deleteUser": {"deleted": "User has been deleted successfully!", "deleting": "Deleting user...", "notDeleted": "User could not be deleted. Please try again."}, "emailVerified": {"verified": "Email verified", "waiting": "Email waiting for verification"}, "impersonate": "Impersonate", "impersonation": {"impersonating": "Impersonating as {name}..."}, "loading": "Loading users...", "resendVerificationMail": {"error": "Could not resend verification mail. Please try again.", "submitting": "Resending verification mail...", "success": "Verification mail has been sent.", "title": "Resend verification mail"}, "search": "Search for name or email...", "title": "Manage users", "assignAdminRole": "Assign admin role", "removeAdminRole": "Remove admin role"}, "description": "Manage your application."}, "app": {"menu": {"accountSettings": "Account settings", "admin": "Admin", "aiChatbot": "AI Chatbot", "organizationSettings": "Organization settings", "start": "Start"}, "userMenu": {"accountSettings": "Account settings", "colorMode": "Color mode", "documentation": "Documentation", "home": "Home", "logout": "Logout"}}, "auth": {"errors": {"invalidEmailOrPassword": "The credentials you entered are invalid. Please check them and try again.", "unknown": "Something went wrong. Please try again.", "userNotFound": "This user does not exists", "failedToCreateUser": "Could not create user. Please try again.", "failedToCreateSession": "Could not create a session. Please try again.", "failedToUpdateUser": "Could not update user. Please try again.", "failedToGetSession": "Could not get the session.", "invalidPassword": "The entered password is incorrect.", "invalidEmail": "The entered email is invalid.", "invalidToken": "The token you entered is invalid or has expired.", "credentialAccountNotFound": "Account not found.", "emailCanNotBeUpdated": "Email could not be updated. Please try again.", "emailNotVerified": "Please verify your email first before logging in.", "failedToGetUserInfo": "Could not load user information.", "idTokenNotSupported": "ID token is not supported.", "passwordTooLong": "Password is too long.", "passwordTooShort": "Password is too short.", "providerNotFound": "This provider is not suppported.", "socialAccountAlreadyLinked": "This account is already linked to a user.", "userEmailNotFound": "<PERSON><PERSON> not found.", "userAlreadyExists": "This user already exists.", "invalidInvitation": "The invitation is invalid or expired.", "sessionExpired": "The session has expired.", "failedToUnlinkLastAccount": "Failed to unlink account", "accountNotFound": "Account not found"}, "forgotPassword": {"backToSignin": "Back to signin", "email": "Email", "hints": {"linkNotSent": {"message": "We are sorry, but we were unable to send you a link to reset your password. Please try again later.", "title": "<PERSON> not sent"}, "linkSent": {"message": "We have sent you a link to continue. Please check your inbox.", "title": "<PERSON> sent"}}, "message": "Please enter your email address and we will send you a link to reset your password.", "submit": "Send link", "title": "Forgot your password?"}, "login": {"continueWith": "Or continue with", "createAnAccount": "Create an account", "dontHaveAnAccount": "Don't have an account yet?", "forgotPassword": "Forgot password?", "hints": {"invalidCredentials": "The email or password you entered are invalid. Please try again.", "linkSent": {"message": "We have sent you a link to continue. Please check your inbox.", "title": "<PERSON> sent"}}, "loginWithPasskey": "Login with passkey", "modes": {"magicLink": "Magic link", "password": "Password"}, "submit": "Sign in", "subtitle": "Please enter your credentials to sign in.", "title": "Welcome back", "sendMagicLink": "Send magic link"}, "resetPassword": {"backToSignin": "Back to signin", "hints": {"error": "We are sorry, but we were unable to reset your password. Please try again.", "success": "Your password has been reset successfully."}, "message": "Please enter a new password.", "newPassword": "New password", "submit": "Reset password", "title": "Reset your password"}, "signup": {"alreadyHaveAccount": "Already have an account?", "email": "Email", "hints": {"signupFailed": "We are sorry, but we were unable to create your account. Please try again later.", "verifyEmail": "We have sent you a link to verify your email. Please check your inbox."}, "message": "We are happy that you want to join us. Please fill in the form below to create your account.", "name": "Name", "password": "Password", "signIn": "Sign in", "submit": "Create account", "title": "Create an account"}, "verify": {"title": "Verify your account", "code": "One-time password", "submit": "Verify", "backToSignin": "Back to login", "message": "Enter the one-time password from your authenticator app to continue."}}, "blog": {"description": "Discover professional AI Fanfic Generator guides, tips and examples. Learn how to write creative fanfiction stories - with practical tips and AI assistance for every fandom.", "title": "AI Fanfic Generator Guide - Tips, Examples & Instructions", "back": "Back to guide"}, "changelog": {"description": "Stay up to date with the latest changes in our product.", "title": "Changelog"}, "common": {"appName": "AI Fanfic Generator", "confirmation": {"cancel": "Cancel", "confirm": "Confirm"}, "menu": {"blog": "Blog", "changelog": "Changelog", "contact": "Contact", "createSpeech": "Fanfic Generator", "dashboard": "Dashboard", "docs": "Docs", "faq": "FAQ", "freeTools": "Free Tools", "howItWorks": "How it works", "login": "<PERSON><PERSON>", "pricing": "Pricing"}, "tableOfContents": {"title": "On this page"}, "actions": {"continue": "Continue", "verify": "Verify"}, "footer": {"product": "Product", "company": "Company", "createFanfic": "Create Fanfic", "howItWorks": "How it works", "pricing": "Pricing", "faq": "FAQ", "blog": "Blog", "contact": "Contact", "freeTools": "Free Tools", "speechTimer": "Text Counter", "storyIdeaGenerator": "Story Idea Generator"}}, "contact": {"title": "Contact & Information", "description": "Learn more about our service and get in touch with us.", "about": {"title": "About AI Fanfic Generator", "description": "AI Fanfic Generator is an innovative service that helps you create creative and engaging fanfiction stories. Our AI-powered technology combines your ideas and characters with professional storytelling to create unique fanfic stories.", "features": {"title": "Our Services", "personalized": "Personalized fanfiction based on your input", "professional": "Professional structure and engaging writing style", "quick": "Quick creation in just minutes", "editable": "Fully editable and customizable", "private": "Confidential and secure"}}, "privacy": {"title": "Privacy & Data Protection", "description": "Your privacy is important to us. All your personal information and memories are treated confidentially.", "points": {"noStorage": "We do not permanently store your personal stories", "encryption": "All data transmissions are encrypted", "noSharing": "Your data is never shared with third parties", "gdpr": "Fully GDPR compliant", "deletion": "You can request deletion of your data at any time"}}, "contact": {"title": "Contact Us", "description": "Do you have questions or need support? We are here to help you.", "email": "Email: <EMAIL>", "response": "Response time: Within 24 hours", "support": "Free support during beta phase", "website": "Website: www.fanficgenerator.com", "hours": "Availability: Monday to Friday, 9:00 AM - 6:00 PM"}, "form": {"email": "Email", "message": "Message", "name": "Name", "notifications": {"error": "We are sorry, but we were unable to send your message. Please try again later.", "success": "Your message has been sent successfully. We will get back to you as soon as possible."}, "submit": "Send message"}}, "documentation": {"title": "Documentation"}, "faq": {"description": "Do you have any questions? We have got you covered.", "title": "Frequently asked questions"}, "mail": {"common": {"openLinkInBrowser": "If you want to open the link in a different browser than your default one, copy and paste this link:", "otp": "One-time password", "useLink": "or use the following link:"}, "emailVerification": {"body": "Hey,\nplease click the link below to verify this new email address.", "confirmEmail": "Verify email", "subject": "Verify your email"}, "forgotPassword": {"body": "Hey,\nyou requested a password reset.\n\nClick the button below to reset your password.", "resetPassword": "Reset password", "subject": "Reset your password"}, "magicLink": {"body": "Hey,\nyou requested a login email from supastarter.\n\nClick the link below to login.", "login": "<PERSON><PERSON>", "subject": "Login to supastarter"}, "newUser": {"body": "Hey,\nthanks for signing up for supastarter.\n\nTo start using our app, please confirm your email address by clicking the link below.", "confirmEmail": "Confirm email", "subject": "Confirm your email"}, "newsletterSignup": {"body": "Thank you for signing up for the supastarter newsletter. We will keep you updated with the latest news and updates.", "subject": "Welcome to our newsletter"}, "organizationInvitation": {"body": "You have been invited to join the organization {organizationName}. Click the button below or copy and paste the link into your browser of choice to accept the invitation and join the organization.", "headline": "Join the organization {organizationName}", "join": "Join the organization", "subject": "You have been invited to join an organization"}}, "newsletter": {"email": "Email", "hints": {"error": {"message": "Could not subscribe to newsletter. Please try again later."}, "success": {"message": "Thank you for subscribing to our newsletter. We will keep you posted.", "title": "Subscribed"}}, "submit": "Subscribe", "subtitle": "Be among the first to get access to supastarter.nextjs.", "title": "Get early access"}, "onboarding": {"account": {"avatar": "Avatar", "avatarDescription": "Click the circle or drop an image to it to upload your avatar.", "name": "Name"}, "continue": "Continue", "message": "Just a few quick steps to get you started.", "notifications": {"accountSetupFailed": "We are sorry, but we were unable to set up your account. Please try again later."}, "step": "Step {step} / {total}", "title": "Set up your account"}, "organizations": {"createForm": {"name": "Organization name", "notifications": {"error": "We are sorry, but we were unable to create your organization. Please try again later.", "success": "Your organization has been created. You can now invite members."}, "submit": "Create organization", "subtitle": "Enter a name for your organization to get started. You can change the name later in the organization settings.", "title": "Create an organization"}, "invitationAlert": {"description": "You need to sign in or create an account to join the organization.", "title": "You have been invited to join an organization."}, "invitationModal": {"accept": "Accept", "decline": "Decline", "description": "You have been invited to join the organization {organizationName}. Do you want to accept the invitation and join the organization?", "title": "Join the organization"}, "organizationSelect": {"createNewOrganization": "Create new organization", "organizations": "Organizations", "personalAccount": "Personal account"}, "organizationsGrid": {"createNewOrganization": "Create new organization", "title": "Your organizations"}, "roles": {"admin": "Admin", "member": "Member", "owner": "Owner"}, "settings": {"changeName": {"title": "Organization name"}, "deleteOrganization": {"confirmation": "Are you sure you want to delete your organization?", "description": "Permanently delete your organization. Once you delete your organization, there is no going back. To confirm, please enter your password below:", "submit": "Delete organization", "title": "Delete organization"}, "logo": {"description": "Upload a logo for your organization.", "title": "Organization logo"}, "members": {"activeMembers": "Active members", "description": "See all active members and the pending invites of your organization.", "invitations": {"empty": "You have not invited any members yet.", "expiresAt": "Expires at {date}", "invitationStatus": {"accepted": "Accepted", "canceled": "Canceled", "pending": "Pending", "rejected": "Rejected"}, "revoke": "Revoke invitation", "resend": "Resend invitation"}, "inviteMember": {"description": "To invite a new member, send them an invitation.", "email": "Email", "notifications": {"error": {"description": "We were unable to invite the member. Please try again later.", "title": "Could not invite member"}, "success": {"description": "The member has been invited.", "title": "Member invited"}}, "role": "Role", "submit": "Invite", "title": "Invite member"}, "leaveOrganization": "Leave organization", "notifications": {"removeMember": {"error": {"description": "Could not remove the member from your organization. Please try again."}, "loading": {"description": "Removing member from organization..."}, "success": {"description": "The member has been successfully removed from your organization."}}, "revokeInvitation": {"error": {"description": "The invitation could not be revoked. Please try again later."}, "loading": {"description": "Revoking invitation..."}, "success": {"description": "The invitation has been revoked."}}, "updateMembership": {"error": {"description": "Could not update organization membership. Please try again."}, "loading": {"description": "Updating membership..."}, "success": {"description": "Membership was updated successfully"}}, "resendInvitation": {"loading": {"description": "Resending invitation..."}, "success": {"description": "Invitation sent"}, "error": {"description": "Could not sent invitation. Please try again."}}}, "pendingInvitations": "Pending invitations", "removeMember": "Remove member", "title": "Members"}, "notifications": {"organizationDeleted": "Your organization has been deleted.", "organizationNameNotUpdated": "We were unable to update your organization name. Please try again later.", "organizationNameUpdated": "Your organization name has been updated.", "organizationNotDeleted": "We were unable to delete your organization. Please try again later."}, "subtitle": "Manage the settings of the organization.", "title": "Organization", "dangerZone": {"title": "Danger zone"}}, "storyIdeaGenerator": {"title": "Story Idea Generator", "description": "Generate unique, creative fanfiction story ideas for any fandom. Get instant inspiration for your next story with our AI-powered idea generator.", "form": {"title": "Story Idea Generator", "description": "Enter your favorite fandom and get a unique, creative story idea to spark your imagination.", "fandomInput": "Fandom or Universe", "fandomPlaceholder": "e.g., <PERSON>, Marvel, Star Wars, Anime...", "fandomHelp": "Enter any book, movie, TV show, game, or fictional universe you'd like to write about.", "generate": "Generate Story Idea", "generating": "Generating...", "tryExample": "Try Example", "copy": "Copy Idea", "copied": "Copied!"}, "result": {"title": "Your Story Idea", "fandom": "Fandom", "storyIdea": "Story Idea", "generatedAt": "Generated at"}, "tips": {"title": "Writing Tips", "tip1": "Use the generated idea as a starting point and expand it with your own creativity.", "tip2": "Consider the characters, setting, and conflicts that would make your story unique.", "tip3": "Don't be afraid to combine elements from different ideas to create something new."}, "features": {"title": "Why Use Our Story Idea Generator?", "creative": {"title": "Creative & Unique", "description": "Get original story ideas that spark your imagination and overcome writer's block"}, "instant": {"title": "Instant Results", "description": "Generate story ideas in seconds - no waiting, no registration required"}, "free": {"title": "Completely Free", "description": "Use our tool unlimited times without any cost or restrictions"}, "unlimited": {"title": "Unlimited Ideas", "description": "Generate as many story ideas as you need for all your favorite fandoms"}}, "howItWorks": {"title": "How It Works", "step1": {"title": "Choose Your Fandom", "description": "Enter the name of any book, movie, TV show, game, or fictional universe"}, "step2": {"title": "Generate Ideas", "description": "Click the generate button to get a unique, creative story idea"}, "step3": {"title": "Start Writing", "description": "Use the idea as inspiration to write your own amazing fanfiction story"}}, "cta": {"title": "Ready to Write Your Story?", "description": "Our AI Fanfic Generator can help you turn your story ideas into complete, engaging fanfiction.", "button": "Create Full Story with AI"}}, "start": {"subtitle": "Welcome to the start page of this organization!"}}, "aiFanficGenerator": {"hero": {"title": "AI Fanfic Generator - Creative Fanfiction with AI", "subtitle": "Write engaging fanfiction stories paragraph by paragraph", "description": "Our AI Fanfic Generator helps you write unique and creative fanfiction stories that bring your favorite characters and worlds to life, one paragraph at a time.", "cta": "Start Writing", "ctaSecondary": "Learn More", "badge": {"new": "New:", "freePhase": "Free during the introductory phase"}, "trustSection": {"title": "Trusted Technology", "aiPowered": "AI-powered", "empathetic": "Creative", "securePrivate": "Secure & Private"}}, "howItWorks": {"title": "How It Works", "subtitle": "Create your personal fanfiction in three simple steps", "steps": {"step1": {"title": "Set Up Your Story", "description": "Choose your fandom, characters, and basic story settings"}, "step2": {"title": "Write Paragraph by Paragraph", "description": "Guide the AI to generate each paragraph based on your direction"}, "step3": {"title": "Edit & Save", "description": "Edit paragraphs as needed and save your complete story"}}, "features": {"quickEasy": {"title": "Interactive Writing", "description": "Write your story paragraph by paragraph with AI assistance"}, "personalDignified": {"title": "Full Control", "description": "Guide the story direction and edit any paragraph as you wish"}, "freeAvailable": {"title": "Free Available", "description": "Use our AI Fanfic Generator completely free during the introductory phase"}}, "cta": "Start Writing Now"}, "form": {"title": "AI Fanfic Generator", "subtitle": "Write your fanfiction story paragraph by paragraph with AI assistance", "setupTitle": "Story Setup", "setupSubtitle": "Set up your story basics to get started", "writingTitle": "Writing Your Story", "writingSubtitle": "Guide the AI to write your story paragraph by paragraph", "startWriting": "Start Writing Story", "backToSetup": "Back to Setup", "nextParagraph": "Next Paragraph", "generating": "Generating...", "regenerateLastParagraph": "Regenerate Last", "editParagraph": "Edit", "deleteParagraph": "Delete", "saveParagraph": "Save", "cancelEdit": "Cancel", "copyStory": "<PERSON><PERSON>", "saveStory": "Save Story", "promptPlaceholder": "Describe what you want to happen next in the story...", "promptLabel": "What should happen next? (optional)", "storyPlaceholder": "The first paragraph is crucial. Please review and edit it carefully, as the rest of the story's style will be based on it. Start by giving some guidance below.", "questions": {"title": {"label": "Title of your fanfiction", "placeholder": "Enter a creative title"}, "fandom": {"label": "Fandom/Universe", "placeholder": "e.g., <PERSON>, Marvel, Anime, etc."}, "genre": {"label": "Genre", "placeholder": "e.g., Romance, Adventure, Mystery, Drama"}, "mainCharacters": {"label": "Main Characters", "placeholder": "e.g., <PERSON>, <PERSON><PERSON><PERSON>"}, "characterRelationships": {"label": "Character Relationships", "placeholder": "Describe the relationships between characters"}, "setting": {"label": "Setting/Location", "placeholder": "Where and when does your story take place?"}, "plotOutline": {"label": "Plot Outline", "placeholder": "Describe the basic plot of your story"}, "length": {"label": "Desired Length", "placeholder": "e.g., Short story, Medium length, Long chapter"}, "tone": {"label": "Writing Style/Tone", "placeholder": "e.g., Romantic, Humorous, Dramatic, Adventurous"}, "contentRating": {"label": "Content Rating", "placeholder": "e.g., General, Teen, Mature"}, "specificRequests": {"label": "Special Requests", "placeholder": "Specific scenes, dialogues, or elements you want included"}, "avoidContent": {"label": "Content to Avoid", "placeholder": "Topics or content that should not appear in the story"}}}, "result": {"title": "Your Fanfiction", "subtitle": "Here is your personalized fanfiction story. You can customize it as you wish.", "speechDuration": "Estimated reading time", "minutes": "minutes", "words": "words", "copy": "Copy", "download": "Download", "edit": "Edit", "regenerate": "Regenerate"}, "features": {"title": "Why Choose Our AI Fanfic Generator?", "subtitle": "Discover the powerful features that make our AI fanfic generator the best choice for creating amazing fanfiction stories across all your favorite fandoms.", "aiPowered": {"title": "Advanced AI Technology", "description": "Our AI fanfic generator uses cutting-edge artificial intelligence trained specifically on fanfiction writing. The AI understands character development, plot structure, and fandom-specific elements to create authentic, engaging stories that feel natural and compelling."}, "allFandoms": {"title": "Support for All Fandoms", "description": "Whether you love <PERSON>, <PERSON>, <PERSON>ime, or any other fandom, our AI fanfic generator can create stories for any fictional universe. The AI fanfic generator adapts to different worlds, maintaining consistency with established lore and character personalities."}, "creative": {"title": "Unlimited Creative Possibilities", "description": "The AI fanfic generator offers endless creative potential. Generate romantic stories, action adventures, comedy, drama, or any genre you desire. Our AI fanfic generator helps you explore new storylines and character relationships, with full customization options for writing style, tone, and story length."}, "cta": {"text": "Ready to create your next masterpiece? Our AI fanfic generator is completely free and ready to help you write amazing fanfiction stories.", "button": "Start Writing Now"}}, "faq": {"title": "Frequently Asked Questions", "questions": {"q1": {"question": "How does the AI Fanfic Generator work?", "answer": "Our AI Fanfic Generator analyzes your story inputs like characters, setting, and plot and creates a creative, personalized fanfiction story based on your specifications."}, "q2": {"question": "Is the AI Fanfic Generator really free?", "answer": "Yes, during our introductory phase, our AI Fanfic Generator is completely free. You can create unlimited fanfiction stories."}, "q4": {"question": "How long does it take to create a fanfiction?", "answer": "Creation takes only a few minutes. After entering your story details, our AI immediately generates a draft of your fanfiction."}, "q5": {"question": "Is my story data stored?", "answer": "No, our system does not store user data. All story information you enter is used exclusively for the immediate creation of your fanfiction and is not stored afterwards. Your privacy and the protection of your creative ideas are our highest priority."}, "q6": {"question": "Can AI really write creative fanfiction?", "answer": "Our AI is a powerful tool that helps you transform your creative ideas into engaging stories. The AI combines your inputs with extensive knowledge of storytelling techniques to create unique fanfiction. It's your creative partner that helps bring your visions to life."}, "q7": {"question": "Can I use the generated fanfiction directly?", "answer": "We recommend that you review the generated fanfiction and adapt it to your preferences. The AI creates a solid first draft, but your personal touch and creativity make the story truly unique. Consider the AI-generated story as a starting point for your own creative work."}, "q8": {"question": "What fandoms does the AI Fanfic Generator support?", "answer": "Our AI Fanfic Generator supports all popular fandoms including <PERSON>, Marvel, DC Comics, Anime series, TV shows, movies, books, and games. The AI fanfic generator can create stories for any fictional universe you specify, making it the most versatile fanfiction generator available."}, "q9": {"question": "How does the AI Fanfic Generator ensure story quality?", "answer": "Our AI Fanfic Generator uses advanced language models trained on high-quality literature and fanfiction. The AI fanfic generator analyzes character development, plot structure, and writing style to produce engaging, well-written fanfiction stories that maintain consistency with your chosen fandom."}, "q10": {"question": "Can I customize the writing style in the AI Fanfic Generator?", "answer": "Yes! Our AI Fanfic Generator allows you to specify writing styles, tone, and genre preferences. Whether you want romantic, action-packed, comedic, or dramatic fanfiction, the AI fanfic generator adapts to create stories that match your desired style and mood."}, "q11": {"question": "How long are the stories created by the AI Fanfic Generator?", "answer": "The AI Fanfic Generator can create stories of various lengths, from short one-shots to multi-chapter epics. You can specify your preferred story length, and the AI fanfic generator will structure the narrative accordingly, ensuring proper pacing and character development throughout."}}}}, "pricing": {"choosePlan": "Choose plan", "contactSales": "Contact sales", "description": "During our introductory phase, our AI fanfic generator is completely free.", "getStarted": "Get started now", "month": "month", "monthly": "Monthly", "freePlan": {"badge": "Introductory Offer", "title": "Free", "price": "$0", "period": "/forever", "description": "Completely free during the introductory phase", "cta": "Start Writing Stories", "features": {"unlimited": "Create unlimited fanfiction stories", "aiPowered": "AI-powered story generation", "instant": "Instant story creation", "editable": "Full editing and customization", "noCosts": "No hidden costs", "privacy": "Privacy guaranteed"}}, "futureNotice": "After the introductory phase, we will introduce fair pricing plans.", "products": {"basic": {"description": "Perfect for getting started", "features": {"anotherFeature": "Another amazing feature", "limitedSupport": "Limited support"}, "title": "Basic"}, "enterprise": {"description": "Custom plan tailored to your requirements", "features": {"enterpriseSupport": "Enterprise support", "unlimitedProjects": "Unlimited projects"}, "title": "Enterprise"}, "free": {"description": "Start for free", "features": {"anotherFeature": "Another amazing feature", "limitedSupport": "Limited support"}, "title": "Free"}, "lifetime": {"description": "Buy once. Use forever.", "features": {"extendSupport": "Extended support", "noRecurringCosts": "No recurring costs"}, "title": "Lifetime"}, "pro": {"description": "Best for teams", "features": {"anotherFeature": "Another amazing feature", "fiveMembers": "Up to 5 members", "fullSupport": "Full support"}, "title": "Pro"}}, "purchase": "Purchase", "recommended": "Recommended", "subscribe": "Subscribe", "title": "AI Fanfic Generator Pricing", "trialPeriod": "{days} {days, plural, one {day} other {days}} free trial", "year": "year", "yearly": "Yearly", "perSeat": "seat"}, "settings": {"account": {"avatar": {"description": "To change your avatar click the picture in this block and select a file from your computer to upload.", "notifications": {"error": "Could not update avatar", "success": "Avatar was updated successfully"}, "title": "Your avatar"}, "changeEmail": {"description": "To change your email, enter the new email and hit save. You will have to confirm the new email before it will become active.", "notifications": {"error": "Could not update email", "success": "Email was updated successfully"}, "title": "Your email"}, "changeName": {"notifications": {"error": "Could not update name", "success": "Name was updated successfully"}, "title": "Your name"}, "deleteAccount": {"confirmation": "Are you sure you want to delete your account?", "description": "Permanently delete your account. Once you delete your account, there is no going back. To confirm, please enter your password below:", "notifications": {"error": "Could not delete account", "success": "Account was deleted successfully"}, "submit": "Delete account", "title": "Delete account"}, "language": {"description": "To change the language of the app for your account, select a language from the list and click save.", "notifications": {"error": "Could not update language", "success": "Language was updated successfully"}, "title": "Your language"}, "security": {"activeSessions": {"description": "These are all the active sessions of your account. Click the X to end a specifc session.", "title": "Active sessions", "notifications": {"revokeSession": {"success": "Session revoked"}}, "currentSession": "Current session"}, "changePassword": {"currentPassword": "Current password", "newPassword": "New password", "notifications": {"error": "Could not update password", "success": "Password was updated successfully"}, "title": "Your password"}, "connectedAccounts": {"connect": "Connect", "disconnect": "Disconnect", "title": "Connected accounts"}, "passkeys": {"description": "Use passkeys as a secure alternative to passwords.", "notifications": {"addPasskey": {"error": {"title": "Could not add passkey"}, "success": {"title": "Passkey added"}}, "deletePasskey": {"error": {"title": "Could not delete passkey"}, "loading": {"title": "Deleting passkey..."}, "success": {"title": "Passkey deleted"}}}, "title": "Passkeys"}, "setPassword": {"description": "You have not set a password yet. To set one, you need to go through the password reset flow. Click the button below to send an email to reset your password and follow the instructions in the email.", "notifications": {"error": "Could not send link to set password. Please try again.", "success": "Check your inbox for the link to set your password."}, "submit": "Set password", "title": "Your password"}, "title": "Security", "twoFactor": {"title": "Two-factor authentication", "description": "Add an extra layer of security to your account.", "dialog": {"password": {"title": "Verify with password", "description": "Please verify your account by entering your password:", "label": "Your password:"}, "totpUrl": {"description": "Use your preferred authenticator app and scan the QR code with it or enter the secret below manually to set up two-factor authentication.", "code": "Enter 6-digit code to verify the setup:", "title": "Enable two-factor authentication"}}, "enable": "Enable two-factor authentication", "notifications": {"verify": {"success": {"title": "Two-factor authentication has been enabled successfully."}}, "enable": {"error": {"title": "Could not verify your account with the provided password. Please try again."}}, "disable": {"success": {"title": "Two-factor authentication has been disabled successfully."}}}, "disable": "Disable two-factor authentication", "enabled": "You have two-factor authentication enabled for your account."}}, "subtitle": "Manage the settings of your personal account.", "title": "Account settings"}, "billing": {"createCustomerPortal": {"label": "Manage billing", "notifications": {"error": {"title": "Could not create a customer portal session. Please try again."}}}, "activePlan": {"status": {"active": "Active", "canceled": "Canceled", "expired": "Expired", "incomplete": "Incomplete", "past_due": "Past due", "paused": "Paused", "trialing": "Trialing", "unpaid": "Unpaid"}, "title": "Your plan"}, "changePlan": {"description": "Choose a plan to subscribe to.", "title": "Change your plan"}, "title": "Billing"}, "menu": {"account": {"billing": "Billing", "dangerZone": "Danger zone", "general": "General", "security": "Security", "title": "Account"}, "organization": {"billing": "Billing", "general": "General", "members": "Members", "title": "Organization", "dangerZone": "Danger zone"}}, "save": "Save"}, "textCounter": {"title": "Text Counter & Analyzer", "description": "Count characters, words, sentences, and paragraphs in your text. Perfect for writers, students, and content creators.", "form": {"textInput": "Text to Analyze", "textPlaceholder": "Paste or type your text here to get detailed statistics...", "readingSpeed": "Reading Speed (WPM)", "readingSpeedHelp": "Average reading speed is 200-250 WPM. Slow: 150-200, Normal: 200-250, Fast: 250-300", "analyze": "Analyze Text", "tryExample": "Try Example", "liveCount": "Live word count", "words": "words", "copy": "Copy Results", "copied": "Copied!"}, "results": {"title": "Text Analysis Results", "characters": "Characters", "charactersNoSpaces": "Characters (no spaces)", "words": "Words", "sentences": "Sentences", "paragraphs": "Paragraphs", "readingTime": "Reading Time", "minute": "minute", "minutes": "minutes", "tips": {"title": "Writing Tips", "characters": "Character count is useful for social media posts and content with strict limits", "words": "Word count helps track writing goals and meet content requirements", "readability": "Shorter sentences and paragraphs improve readability", "structure": "Well-structured text with clear paragraphs enhances user experience"}}, "features": {"title": "Why Use Our Text Counter?", "accurate": {"title": "Comprehensive Analysis", "description": "Get detailed statistics about your text including characters, words, and structure"}, "customizable": {"title": "Customizable Settings", "description": "Adjust reading speed to get personalized reading time estimates"}, "free": {"title": "Completely Free", "description": "No registration required - use our tool anytime"}, "instant": {"title": "Real-time Counting", "description": "See live updates as you type or paste your text"}}, "cta": {"title": "Need Help Writing Your Story?", "description": "Our AI Fanfic Generator can help you create creative, engaging fanfiction stories.", "button": "Create Fanfic with AI"}}, "start": {"subtitle": "See the latest stats of your awesome business.", "welcome": "Welcome {name}!"}, "zod": {"errors": {"invalid_arguments": "Invalid function arguments", "invalid_date": "Invalid date", "invalid_enum_value": "Invalid enum value. Expected {- options}, received '{received}'", "invalid_intersection_types": "Intersection results could not be merged", "invalid_literal": "Invalid literal value, expected {expected}", "invalid_return_type": "Invalid function return type", "invalid_string": {"cuid": "Invalid {validation}", "datetime": "Invalid {validation}", "email": "Invalid {validation}", "endsWith": "Invalid input: must end with \"{endsWith}\"", "regex": "Invalid", "startsWith": "Invalid input: must start with \"{startsWith}\"", "url": "Invalid {validation}", "uuid": "Invalid {validation}"}, "invalid_type": "Expected {expected}, received {received}", "invalid_type_received_undefined": "Required", "invalid_union": "Invalid input", "invalid_union_discriminator": "Invalid discriminator value. Expected {- options}", "not_finite": "Number must be finite", "not_multiple_of": "Number must be a multiple of {multipleOf}", "too_big": {"array": {"exact": "Array must contain exactly {maximum} element(s)", "inclusive": "Array must contain at most {maximum} element(s)", "not_inclusive": "Array must contain less than {maximum} element(s)"}, "date": {"exact": "Date must be exactly {- maximum, datetime}", "inclusive": "Date must be smaller than or equal to {- maximum, datetime}", "not_inclusive": "Date must be smaller than {- maximum, datetime}"}, "number": {"exact": "Number must be exactly {maximum}", "inclusive": "Number must be less than or equal to {maximum}", "not_inclusive": "Number must be less than {maximum}"}, "set": {"exact": "Invalid input", "inclusive": "Invalid input", "not_inclusive": "Invalid input"}, "string": {"exact": "String must contain exactly {maximum} character(s)", "inclusive": "String must contain at most {maximum} character(s)", "not_inclusive": "String must contain under {maximum} character(s)"}}, "too_small": {"array": {"exact": "Array must contain exactly {minimum} element(s)", "inclusive": "Array must contain at least {minimum} element(s)", "not_inclusive": "Array must contain more than {minimum} element(s)"}, "date": {"exact": "Date must be exactly {- minimum, datetime}", "inclusive": "Date must be greater than or equal to {- minimum, datetime}", "not_inclusive": "Date must be greater than {- minimum, datetime}"}, "number": {"exact": "Number must be exactly {minimum}", "inclusive": "Number must be greater than or equal to {minimum}", "not_inclusive": "Number must be greater than {minimum}"}, "set": {"exact": "Invalid input", "inclusive": "Invalid input", "not_inclusive": "Invalid input"}, "string": {"exact": "String must contain exactly {minimum} character(s)", "inclusive": "String must contain at least {minimum} character(s)", "not_inclusive": "String must contain over {minimum} character(s)"}}, "unrecognized_keys": "Unrecognized key(s) in object: {- keys}"}, "types": {"array": "array", "bigint": "bigint", "boolean": "boolean", "date": "date", "float": "float", "function": "function", "integer": "integer", "map": "map", "nan": "nan", "never": "never", "null": "null", "number": "number", "object": "object", "promise": "promise", "set": "set", "string": "string", "symbol": "symbol", "undefined": "undefined", "unknown": "unknown", "void": "void"}, "validations": {"cuid": "cuid", "cuid2": "cuid2", "datetime": "datetime", "email": "email", "emoji": "emoji", "ip": "up", "regex": "regex", "ulid": "ulid", "url": "url", "uuid": "uuid"}}, "choosePlan": {"title": "Choose your plan", "description": "To continue, please select a plan."}}